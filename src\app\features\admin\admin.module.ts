import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { AccessTypeModalComponent } from '../../shared/components/access-type-modal/access-type-modal.component';

import { AdminRoutingModule } from './admin-routing.module';
import { AdminLayoutComponent } from './admin-layout.component';

// Import admin components from admin module
import { UsersManagementComponent } from './components/users-management/users-management.component';
import { UserDetailComponent } from './components/user-detail/user-detail.component';
import { GamesManagementComponent } from './components/games-management/games-management.component';
import { GameDetailComponent } from './components/game-detail/game-detail.component';
import { LibraryManagementComponent } from './components/library-management/library-management.component';
import { GameFilesManagementComponent } from './components/game-files-management/game-files-management.component';
import { GameAccessManagementComponent } from './components/game-access-management/game-access-management.component';
import { GamePackagesManagementComponent } from './components/game-packages-management/game-packages-management.component';
import { GamePackageDetailComponent } from './components/game-package-detail/game-package-detail.component';

@NgModule({
  declarations: [
    AdminLayoutComponent,
    // Admin components (will be moved here from profile module)
    UsersManagementComponent,
    UserDetailComponent,
    GamesManagementComponent,
    GameDetailComponent,
    LibraryManagementComponent,
    GameFilesManagementComponent,
    GameAccessManagementComponent,
    GamePackagesManagementComponent,
    GamePackageDetailComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    AccessTypeModalComponent,
    AdminRoutingModule
  ]
})
export class AdminModule { }
