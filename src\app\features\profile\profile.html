<!-- Profile Page Container -->
<div
  class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900 pt-20"
>
  <!-- Mobile Sidebar Overlay -->
  <div
    *ngIf="isMobileSidebarOpen"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
    (click)="closeMobileSidebar()"
    aria-hidden="true"
  ></div>

  <div class="flex flex-col lg:flex-row h-[calc(100vh-5rem)] profile-layout">
    <!-- Left Sidebar -->
    <div
      class="fixed top-20 left-0 w-80 h-[calc(100vh-5rem)] bg-slate-900/95 backdrop-blur-sm border-r border-slate-600/30 flex flex-col profile-sidebar z-50 transform transition-transform duration-300 ease-in-out"
      [class.translate-x-0]="isMobileSidebarOpen"
      [class.-translate-x-full]="!isMobileSidebarOpen"
      [class.lg:translate-x-0]="true"
      role="navigation"
      [attr.aria-hidden]="!isMobileSidebarOpen && 'true'"
      aria-label="Навигация профиля"
    >
      <!-- Mobile Close Button - Top Right Cross -->
      <button
        class="lg:hidden absolute top-3 right-3 text-gray-300 hover:text-white transition-colors p-1.5 hover:bg-slate-700/50 rounded-lg z-10"
        (click)="closeMobileSidebar()"
        aria-label="Закрыть меню"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>

      <!-- Navigation Menu -->
      <div class="flex-1 p-3 lg:p-4 space-y-4 lg:space-y-6 overflow-y-auto">

        <!-- Account Section -->
        <div class="mb-4 lg:mb-6">
          <h2
            class="text-white font-medium text-sm lg:text-base mb-2 lg:mb-3 flex justify-between items-center"
          >
            <span> Учетная запись </span>
            <!-- <span> 
              <span
                *ngIf="userProfile?.is_staff"
                class="text-xs bg-blue-900/50 text-blue-400 border
                 border-blue-500/50 self-start sm:self-auto rounded px-1 py-0.5 text-center"
              >
               ID:
                {{userProfile?.user_code}}
              </span>
            </span> -->
          </h2>
          <div class="space-y-1">
            <a
              routerLink="/profile/settings"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Параметры профиля
            </a>
          </div>
        </div>



        <!-- My Games Section -->
        <div class="mb-4 lg:mb-6">
          <h2 class="text-white font-medium text-sm lg:text-base mb-2 lg:mb-3">
            Мои игры
          </h2>
          <div class="space-y-1">
            <a
              routerLink="/profile/library"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left flex items-center justify-between text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              <span>Библиотека</span>
              <span
                *ngIf="userSummary?.library_count !== undefined"
                class="bg-blue-600/80 text-white text-xs px-1.5 lg:px-2 py-0.5 rounded-full min-w-[16px] lg:min-w-[20px] text-center"
              >
                {{ userSummary?.library_count }}
              </span>
            </a>
            <a
              routerLink="/profile/my-packages"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Мой Тариф
            </a>
            <a
              routerLink="/profile/purchases"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left flex items-center justify-between text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              <span>История покупок</span>
              <span
                *ngIf="userSummary && userSummary.pending_payments_count && userSummary.pending_payments_count > 0"
                class="bg-orange-500/80 text-white text-xs px-1.5 lg:px-2 py-0.5 rounded-full min-w-[16px] lg:min-w-[20px] text-center font-medium"
                title="Ожидающие оплаты покупки"
              >
                {{ userSummary.pending_payments_count }}
              </span>
            </a>
          </div>
        </div>

        

        <!-- Store Section
        <div class="mb-4 lg:mb-6">
          <h2 class="text-white font-medium text-sm lg:text-base mb-2 lg:mb-3">
            Магазин
          </h2>
          <div class="space-y-1">
            <div class="space-y-1">
              Temporarily disabled - games catalog moved to main page
              <a
                routerLink="/profile/games"
                routerLinkActive="bg-slate-700/70"
                class="w-full text-left flex items-center justify-between text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
              >
                <span>Каталог игр</span>
              </a> 
            </div>
            
          </div>
        </div> -->

        <!-- Administration Section (Only for Staff) -->
        <div *ngIf="userProfile?.is_staff" class="mb-4 lg:mb-6">
          <h2 class="text-white font-medium text-sm lg:text-base mb-2 lg:mb-3">
            Администрирование
          </h2>
          <div class="space-y-1">
            <a
              routerLink="/admin"
              class="w-full text-left text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer flex items-center"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Панель администратора
            </a>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="p-3 lg:p-4 border-t border-slate-600/30">
        <button
          (click)="onLogout()"
          class="w-full bg-slate-700/60 hover:bg-slate-600/70 text-gray-300 hover:text-white font-normal py-2 lg:py-2.5 px-2 lg:px-3 rounded-md border border-slate-600/40 hover:border-slate-500/60 transition-all text-xs lg:text-sm"
        >
          <svg
            class="w-3 h-3 lg:w-4 lg:h-4 inline mr-1 lg:mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            ></path>
          </svg>
          Выйти
        </button>
      </div>
    </div>

    <!-- Mobile Sidebar Open Button -->
    <div
      *ngIf="!isMobileSidebarOpen"
      class="lg:hidden fixed top-20 left-0 z-40"
    >
      <button
        (click)="toggleMobileSidebar()"
        class="bg-slate-900/95 backdrop-blur-sm border-r border-b border-slate-600/30 text-gray-300 hover:text-white transition-colors px-4 py-2 hover:bg-slate-700/50 rounded-br-lg"
        aria-label="Открыть меню"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5l7 7-7 7"
          ></path>
        </svg>
      </button>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 ml-0 lg:ml-80 profile-content">
      <!-- Content Area -->
      <div class="p-4 lg:p-8 h-[calc(100vh-5rem)] overflow-y-auto">
        <!-- Loading State -->
        <app-loading-spinner *ngIf="isLoading" [overlay]="true">
        </app-loading-spinner>

        <!-- Error State -->
        <div
          *ngIf="errorMessage && !isLoading"
          class="flex items-center justify-center h-64"
        >
          <div class="text-center">
            <div
              class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md"
            >
              <h3 class="text-red-300 font-semibold mb-2">Ошибка загрузки</h3>
              <p class="text-red-200 mb-4">{{ errorMessage }}</p>
              <button
                (click)="loadUserProfile()"
                class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Попробовать снова
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Content -->
        <div *ngIf="userProfile && !isLoading">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
  </div>
</div>
