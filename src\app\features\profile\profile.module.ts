import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Profile } from './profile';
import { SharedModule } from "../../shared/shared.module";
import { AccessTypeModalComponent } from '../../shared/components/access-type-modal/access-type-modal.component';
import { ProfileSettingsComponent } from './components/profile-settings/profile-settings.component';
import { UserLibraryComponent } from './components/user-library/user-library.component';
import { GameKeysManagementComponent } from './components/game-keys-management/game-keys-management.component';
import { PurchaseHistoryComponent } from './components/purchase-history/purchase-history.component';
import { ProfileGamesCatalogComponent } from './components/profile-games-catalog.component';
import { ProfileCartComponent } from './components/profile-cart.component';
import { ProfileGameDetailComponent } from './components/profile-game-detail.component';
import { MyPackagesComponent } from './components/my-packages/my-packages.component';
// Temporarily disabled - packages moved to main page only
// import { ProfilePackagesCatalogComponent } from './components/profile-packages-catalog.component';
// import { ProfilePackageDetailComponent } from './components/profile-package-detail.component';

@NgModule({
    declarations: [
        Profile,
        ProfileSettingsComponent,
        UserLibraryComponent,
        GameKeysManagementComponent,
        PurchaseHistoryComponent,
        ProfileGamesCatalogComponent,
        ProfileCartComponent,
        ProfileGameDetailComponent,
        // Temporarily disabled - packages moved to main page only
        // ProfilePackagesCatalogComponent,
        // ProfilePackageDetailComponent
    ],
    imports: [
        CommonModule,
        RouterModule,
        FormsModule,
        ReactiveFormsModule,
        SharedModule,
        AccessTypeModalComponent,
        MyPackagesComponent
    ]
})
export class ProfileModule { }
