import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    // First check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }

    // Check if user is staff
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.is_staff) {
      return true;
    }

    // If we have a current user but they're not staff, redirect to profile
    if (currentUser) {
      this.router.navigate(['/profile']);
      return false;
    }

    // If no current user data, try to get fresh profile data
    return this.authService.getUserProfile().pipe(
      map(profile => {
        if (profile.is_staff) {
          return true;
        } else {
          this.router.navigate(['/profile']);
          return false;
        }
      })
    );
  }
}
