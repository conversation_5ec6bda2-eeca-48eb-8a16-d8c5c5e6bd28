/* Admin layout specific styles */

/* Ensure proper layout structure */
.admin-layout {
  min-height: calc(100vh - 5rem);
}

/* Admin content area */
.admin-content {
  background: transparent;
  padding: 0;
  margin: 0;
}

/* Mobile sidebar animations */
.admin-sidebar {
  transition: transform 0.3s ease-in-out;
}

/* Mobile-first responsive design */
@media (max-width: 1023px) {
  .admin-layout {
    flex-direction: column;
    height: calc(100vh - 5rem);
  }

  .admin-sidebar {
    /* Mobile sidebar is fixed and hidden by default */
    position: fixed;
    top: 5rem;
    left: 0;
    width: 320px;
    height: calc(100vh - 5rem);
    z-index: 50;
    transform: translateX(-100%);
    border-right: 1px solid rgba(71, 85, 105, 0.3);
    border-bottom: none;
  }

  .admin-sidebar.translate-x-0 {
    transform: translateX(0);
  }

  .admin-sidebar.-translate-x-full {
    transform: translateX(-100%);
  }

  .admin-content {
    width: 100%;
    height: calc(100vh - 5rem);
    overflow-y: auto;
  }
}

/* Desktop layout */
@media (min-width: 1024px) {
  .admin-layout {
    flex-direction: row;
    height: calc(100vh - 5rem);
  }

  .admin-sidebar {
    position: fixed;
    top: 5rem;
    left: 0;
    width: 320px;
    height: calc(100vh - 5rem);
    z-index: 40;
    transform: translateX(0);
    border-right: 1px solid rgba(71, 85, 105, 0.3);
  }

  .admin-content {
    margin-left: 320px;
    width: calc(100% - 320px);
    height: calc(100vh - 5rem);
    overflow-y: auto;
  }
}

/* Prevent body scroll when mobile sidebar is open */
:global(body.mobile-sidebar-open) {
  overflow: hidden;
}

/* Custom scrollbar for sidebar */
.admin-sidebar::-webkit-scrollbar {
  width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.1);
}

.admin-sidebar::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
  border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth transitions for navigation links */
.admin-sidebar a,
.admin-sidebar button {
  transition: all 0.2s ease-in-out;
}

/* Focus styles for accessibility */
.admin-sidebar a:focus,
.admin-sidebar button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Hover effects */
.admin-sidebar a:hover,
.admin-sidebar button:hover {
  transform: translateX(2px);
}

/* Active link styling */
.admin-sidebar a.bg-slate-700\/70 {
  background-color: rgba(51, 65, 85, 0.7);
  color: white;
  font-weight: 500;
}

/* Mobile button styling */
@media (max-width: 1023px) {
  .admin-sidebar a:hover,
  .admin-sidebar button:hover {
    transform: none; /* Disable transform on mobile for better touch experience */
  }
}
