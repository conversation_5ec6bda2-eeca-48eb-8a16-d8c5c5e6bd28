/* Modal animations */
.fixed {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal content animation */
.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Image hover effects */
.aspect-square img {
  transition: transform 0.3s ease;
}

.aspect-square:hover img {
  transform: scale(1.05);
}

/* Button hover effects */
button {
  transition: all 0.2s ease;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Form input focus effects */
input:focus,
textarea:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Badge animations */
.px-3.py-1 {
  transition: all 0.2s ease;
}

.px-3.py-1:hover {
  transform: scale(1.05);
}

/* Loading spinner */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Price highlight */
.text-green-400 {
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

/* Section backgrounds */
.bg-slate-800\/60 {
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.bg-slate-800\/60:hover {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: rgba(148, 163, 184, 0.3);
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2 {
    gap: 0.75rem;
  }

  .grid-cols-1.md\:grid-cols-3 {
    gap: 0.75rem;
  }

  .text-3xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-2xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* File input styling */
input[type="file"] {
  cursor: pointer;
}

input[type="file"]::-webkit-file-upload-button {
  cursor: pointer;
}

/* Confirmation modal specific styles */
.z-60 {
  z-index: 60;
}

/* Error message styling */
.bg-red-500\/20 {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Success state animations */
.transform.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

/* Disabled state styling */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Text truncation utilities */
.truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.truncate-3-lines {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Focus visible for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
