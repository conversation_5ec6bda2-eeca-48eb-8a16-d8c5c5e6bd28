<!-- Game Access Management Content -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="flex items-center justify-between mb-4 lg:mb-6">
    <div class="flex items-center">
      <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-purple-600 to-slate-700 rounded-full flex items-center justify-center mr-3 lg:mr-4">
        <svg class="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <div>
        <h1 class="text-xl lg:text-2xl font-bold text-white">Управление доступом к играм</h1>
        <p class="text-gray-400 text-sm lg:text-base">Просмотр и управление доступом пользователей к играм</p>
      </div>
    </div>
    <button
      (click)="showAddAccessForm()"
      class="px-3 py-2 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center"
    >
      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
      </svg>
      Добавить доступ
    </button>
  </div>

  <!-- Add/Edit Form -->
  <div *ngIf="showAddForm" class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-4 mb-6">
    <h3 class="text-lg font-semibold text-white mb-3">
      {{ editingAccess ? 'Редактировать доступ' : 'Добавить доступ к игре' }}
    </h3>

    <form (ngSubmit)="submitForm()" class="space-y-3">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
        <!-- User Selection -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Пользователь</label>
          <select
            [(ngModel)]="formData.user"
            name="user"
            [disabled]="editingAccess !== null"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
            required
          >
            <option value="0">Выберите пользователя</option>
            <option *ngFor="let user of users" [value]="user.id">{{ user.email }}</option>
          </select>
        </div>

        <!-- Game Selection -->
        <div>
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Игра</label>
          <select
            [(ngModel)]="formData.game"
            name="game"
            [disabled]="editingAccess !== null"
            class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50"
            required
          >
            <option value="0">Выберите игру</option>
            <option *ngFor="let game of games" [value]="game.id">{{ game.title }}</option>
          </select>
        </div>

        <!-- Access Type -->
        <div>
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Тип доступа</label>
          <select
            [(ngModel)]="formData.access_type"
            name="access_type"
            class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
          >
            <option value="oneday">Один день</option>
            <option value="subscription">Подписка</option>
          </select>
        </div>

        <!-- Access Start -->
        <div>
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Начало доступа</label>
          <input
            type="datetime-local"
            [(ngModel)]="formData.access_start"
            name="access_start"
            class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
        </div>

        <!-- Access End -->
        <div class="md:col-span-2">
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Окончание доступа</label>
          <input
            type="datetime-local"
            [(ngModel)]="formData.access_end"
            name="access_end"
            class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="formError" class="bg-red-900/20 border border-red-500/50 rounded-lg p-3">
        <p class="text-red-400 text-sm lg:text-base">{{ formError }}</p>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          (click)="cancelForm()"
          class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
        >
          Отмена
        </button>
        <button
          type="submit"
          [disabled]="formLoading"
          class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center"
        >
          <app-loading-spinner *ngIf="formLoading" class="mr-2"></app-loading-spinner>
          {{ editingAccess ? 'Обновить' : 'Создать' }}
        </button>
      </div>
    </form>
  </div>

  <!-- Search and Filters -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Search Input -->
      <div>
        <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Поиск</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          placeholder="Email пользователя, название игры..."
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm lg:text-base"
        >
      </div>

      <!-- Access Type Filter -->
      <div>
        <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Тип доступа</label>
        <select
          [(ngModel)]="selectedAccessType"
          (ngModelChange)="onAccessTypeFilterChange()"
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm lg:text-base"
        >
          <option value="all">Все типы</option>
          <option value="oneday">Один день</option>
          <option value="subscription">Подписка</option>
        </select>
      </div>

      <!-- Access Status Filter -->
      <div>
        <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Статус доступа</label>
        <select
          [(ngModel)]="selectedAccessStatus"
          (ngModelChange)="onAccessStatusFilterChange()"
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm lg:text-base"
        >
          <option value="all">Все статусы</option>
          <option value="active">Активный</option>
          <option value="expired">Истекший</option>
        </select>
      </div>

      <!-- Sort Options -->
      <div>
        <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Сортировка</label>
        <select
          [(ngModel)]="sortBy"
          (ngModelChange)="onSortChange()"
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm lg:text-base"
        >
          <option value="-access_start">Начало доступа (новые)</option>
          <option value="access_start">Начало доступа (старые)</option>
          <option value="-access_end">Окончание доступа (новые)</option>
          <option value="access_end">Окончание доступа (старые)</option>
          <option value="user_email">Email (А-Я)</option>
          <option value="-user_email">Email (Я-А)</option>
          <option value="game_title">Игра (А-Я)</option>
          <option value="-game_title">Игра (Я-А)</option>
        </select>
      </div>
    </div>

    <!-- Results Summary -->
    <div class="mt-4 flex justify-between items-center text-sm text-gray-400">
      <span>Найдено записей: {{ totalGameAccess }}</span>
      <button
        (click)="loadGameAccess()"
        class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors"
      >
        Обновить
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="gameAccessLoading" class="flex justify-center py-8">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error State -->
  <div *ngIf="gameAccessError && !gameAccessLoading" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
    <p class="text-red-400">{{ gameAccessError }}</p>
    <button (click)="loadGameAccess()" class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors">
      Повторить попытку
    </button>
  </div>

  <!-- Game Access Table -->
  <div *ngIf="!gameAccessLoading && !gameAccessError" class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-slate-800/60">
          <tr>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Пользователь</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Игра</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Тип доступа</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Начало</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Окончание</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Статус</th>
            <th class="px-6 py-3 text-left text-sm lg:text-base font-medium text-gray-300 uppercase tracking-wider">Действия</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-slate-700/50">
          <tr *ngFor="let access of gameAccess" class="hover:bg-slate-700/20 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap text-sm lg:text-base text-gray-300">{{ access.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm lg:text-base text-blue-400">{{ access.user_email }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm lg:text-base text-gray-300">{{ access.game_title }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [ngClass]="getAccessTypeClass(access.access_type)" class="text-sm lg:text-base font-medium">
                {{ getAccessTypeText(access.access_type) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm lg:text-base text-gray-300">{{ formatDate(access.access_start) }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm lg:text-base text-gray-300">{{ formatDate(access.access_end) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [ngClass]="access.has_access ? 'bg-green-900/50 text-green-400 border-green-500/50' : 'bg-red-900/50 text-red-400 border-red-500/50'"
                    class="inline-flex px-2 py-1 text-sm lg:text-base font-semibold rounded-full border">
                {{ access.has_access ? 'Активен' : 'Истек' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm lg:text-base">
              <div class="flex space-x-2">
                <button
                  (click)="editAccess(access)"
                  class="text-blue-400 hover:text-blue-300 transition-colors"
                  title="Редактировать"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                  </svg>
                </button>
                <button
                  (click)="deleteAccess(access)"
                  class="text-red-400 hover:text-red-300 transition-colors"
                  title="Удалить"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="totalPages > 1" class="bg-slate-800/40 px-6 py-3 flex items-center justify-between border-t border-slate-700/50">
      <div class="flex-1 flex justify-between sm:hidden">
        <button
          (click)="onPageChange(currentPage - 1)"
          [disabled]="currentPage === 1"
          class="relative inline-flex items-center px-4 py-2 border border-slate-600 text-sm lg:text-base font-medium rounded-md text-gray-300 bg-slate-800 hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Предыдущая
        </button>
        <button
          (click)="onPageChange(currentPage + 1)"
          [disabled]="currentPage === totalPages"
          class="ml-3 relative inline-flex items-center px-4 py-2 border border-slate-600 text-sm lg:text-base font-medium rounded-md text-gray-300 bg-slate-800 hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Следующая
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm lg:text-base text-gray-400">
            Показано <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> -
            <span class="font-medium">{{ Math.min(currentPage * pageSize, totalGameAccess) }}</span> из
            <span class="font-medium">{{ totalGameAccess }}</span> записей
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
            <button
              (click)="onPageChange(currentPage - 1)"
              [disabled]="currentPage === 1"
              class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-slate-600 bg-slate-800 text-sm lg:text-base font-medium text-gray-300 hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </button>
            <button
              *ngFor="let page of pages"
              (click)="onPageChange(page)"
              [ngClass]="page === currentPage ? 'bg-purple-600 text-white' : 'bg-slate-800 text-gray-300 hover:bg-slate-700'"
              class="relative inline-flex items-center px-4 py-2 border border-slate-600 text-sm lg:text-base font-medium transition-colors"
            >
              {{ page }}
            </button>
            <button
              (click)="onPageChange(currentPage + 1)"
              [disabled]="currentPage === totalPages"
              class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-slate-600 bg-slate-800 text-sm lg:text-base font-medium text-gray-300 hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!gameAccessLoading && !gameAccessError && gameAccess.length === 0" class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <h3 class="mt-2 text-sm lg:text-base font-medium text-gray-300">Нет записей о доступе</h3>
    <p class="mt-1 text-sm lg:text-base text-gray-400">Начните с создания доступа к игре для пользователя.</p>
    <div class="mt-6">
      <button
        (click)="showAddAccessForm()"
        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm lg:text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
      >
        <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
        </svg>
        Добавить доступ
      </button>
    </div>
  </div>
</div>
