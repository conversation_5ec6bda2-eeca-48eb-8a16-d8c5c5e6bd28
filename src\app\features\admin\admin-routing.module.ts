import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminGuard } from '../../core/guards/admin.guard';
import { AdminLayoutComponent } from './admin-layout.component';
import { UsersManagementComponent } from './components/users-management/users-management.component';
import { UserDetailComponent } from './components/user-detail/user-detail.component';
import { GamesManagementComponent } from './components/games-management/games-management.component';
import { LibraryManagementComponent } from './components/library-management/library-management.component';
import { GameFilesManagementComponent } from './components/game-files-management/game-files-management.component';
import { GameAccessManagementComponent } from './components/game-access-management/game-access-management.component';
import { GamePackagesManagementComponent } from './components/game-packages-management/game-packages-management.component';

const routes: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    canActivate: [AdminGuard],
    children: [
      {
        path: '',
        redirectTo: 'users',
        pathMatch: 'full'
      },
      {
        path: 'users',
        component: UsersManagementComponent
      },
      {
        path: 'users/:id',
        component: UserDetailComponent
      },
      {
        path: 'admin-games',
        component: GamesManagementComponent
      },
      {
        path: 'admin-library',
        component: LibraryManagementComponent
      },
      {
        path: 'game-files',
        component: GameFilesManagementComponent
      },
      {
        path: 'game-access',
        component: GameAccessManagementComponent
      },
      {
        path: 'admin-packages',
        component: GamePackagesManagementComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule { }
