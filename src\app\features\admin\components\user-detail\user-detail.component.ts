import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { UserService } from '../../../../core/services/user.service';
import { ModalService } from '../../../../core/services/modal.service';
import { User, UserUpdateRequest } from '../../../../core/models/user.model';

@Component({
  selector: 'app-user-detail',
  standalone: false,
  templateUrl: './user-detail.component.html',
  styleUrl: './user-detail.component.css'
})
export class UserDetailComponent implements OnInit, OnDestroy {
  user: User | null = null;
  userForm: FormGroup;
  loading = false;
  error = '';
  isEditing = false;
  saving = false;
  
  private routeSubscription?: Subscription;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private modalService: ModalService,
    private fb: FormBuilder
  ) {
    this.userForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      is_active: [true],
      is_staff: [false],
      password: ['']
    });
  }

  ngOnInit(): void {
    this.routeSubscription = this.route.params.subscribe(params => {
      const userId = +params['id'];
      if (userId) {
        this.loadUser(userId);
      }
    });
  }

  ngOnDestroy(): void {
    this.routeSubscription?.unsubscribe();
  }

  loadUser(userId: number): void {
    this.loading = true;
    this.error = '';

    this.userService.getUser(userId).subscribe({
      next: (user) => {
        this.user = user;
        this.populateForm();
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load user:', error);
        this.error = 'Не удалось загрузить данные пользователя';
        this.loading = false;
      }
    });
  }

  populateForm(): void {
    if (this.user) {
      this.userForm.patchValue({
        email: this.user.email,
        phone: this.user.phone || '',
        is_active: this.user.is_active,
        is_staff: this.user.is_staff,
        password: '' // Always start with empty password for security
      });
    }
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing) {
      // Reset form when canceling edit
      this.populateForm();
    }
  }

  onSubmit(): void {
    if (this.userForm.valid && this.user) {
      this.saving = true;
      
      const updateData: UserUpdateRequest = {
        email: this.userForm.value.email,
        phone: this.userForm.value.phone || null,
        is_active: this.userForm.value.is_active,
        is_staff: this.userForm.value.is_staff
      };

      // Only include password if it's provided
      if (this.userForm.value.password && this.userForm.value.password.trim()) {
        updateData.password = this.userForm.value.password;
      }

      this.userService.updateUser(this.user.id, updateData).subscribe({
        next: (updatedUser) => {
          this.user = updatedUser;
          this.isEditing = false;
          this.saving = false;
          this.modalService.success('Успех', 'Данные пользователя успешно обновлены');
        },
        error: (error) => {
          console.error('Failed to update user:', error);
          this.saving = false;
          let errorMessage = 'Не удалось обновить данные пользователя';
          
          if (error.error && typeof error.error === 'object') {
            const errors = [];
            for (const [field, messages] of Object.entries(error.error)) {
              if (Array.isArray(messages)) {
                errors.push(`${field}: ${messages.join(', ')}`);
              }
            }
            if (errors.length > 0) {
              errorMessage += ':\n' + errors.join('\n');
            }
          } else if (error.message) {
            errorMessage += ': ' + error.message;
          }
          
          this.modalService.error('Ошибка', errorMessage);
        }
      });
    }
  }

  deleteUser(): void {
    if (!this.user) return;

    this.modalService.confirm(
      'Удаление пользователя',
      `Вы уверены, что хотите удалить пользователя ${this.user.email}?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed && this.user) {
        this.userService.deleteUser(this.user.id).subscribe({
          next: () => {
            this.modalService.success('Успех', 'Пользователь успешно удален');
            this.router.navigate(['/profile/users']);
          },
          error: (error) => {
            console.error('Failed to delete user:', error);
            let errorMessage = 'Не удалось удалить пользователя';

            if (error.status === 403) {
              errorMessage = 'Суперпользователя нельзя удалить';
            } else if (error.message) {
              errorMessage += ': ' + error.message;
            }

            this.modalService.error('Ошибка', errorMessage);
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/profile/users']);
  }

  getUserRoleText(user: User): string {
    if (user.is_superuser) return 'Суперпользователь';
    if (user.is_staff) return 'Администратор';
    return 'Пользователь';
  }

  getUserRoleClass(user: User): string {
    if (user.is_superuser) return 'text-red-400';
    if (user.is_staff) return 'text-yellow-400';
    return 'text-blue-400';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
