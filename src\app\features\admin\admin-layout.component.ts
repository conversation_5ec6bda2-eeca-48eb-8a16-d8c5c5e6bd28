import { Component, OnInit, On<PERSON><PERSON>roy, Renderer2 } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../core/services/auth.service';
import { UserSummaryService } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { UserProfile, UserSummary } from '../../core/models/auth.model';

@Component({
  selector: 'app-admin-layout',
  standalone: false,
  templateUrl: './admin-layout.component.html',
  styleUrl: './admin-layout.component.css'
})
export class AdminLayoutComponent implements OnInit, OnDestroy {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // User summary data
  userSummary: UserSummary | null = null;
  private summarySubscription?: Subscription;
  private routerSubscription?: Subscription;

  // Mobile sidebar state
  isMobileSidebarOpen = false;

  constructor(
    private authService: AuthService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router,
    private route: ActivatedRoute,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadUserSummary();
    this.setupRouterSubscription();
  }

  ngOnDestroy(): void {
    this.summarySubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
    this.renderer.removeClass(document.body, 'mobile-sidebar-open');
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
        
        // Check if user is still staff, redirect if not
        if (!profile.is_staff) {
          this.modalService.error('Доступ запрещен', 'У вас нет прав доступа к панели администратора');
          this.router.navigate(['/profile']);
        }
      },
      error: (error: any) => {
        console.error('Failed to load user profile:', error);
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  loadUserSummary(): void {
    this.summarySubscription = this.userSummaryService.getUserSummary().subscribe({
      next: (summary) => {
        this.userSummary = summary;
      },
      error: (error: any) => {
        console.error('Failed to load user summary:', error);
      }
    });
  }

  // Setup router subscription to close sidebar on navigation
  setupRouterSubscription(): void {
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.closeMobileSidebar();
      });
  }

  // Mobile sidebar methods
  toggleMobileSidebar(): void {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
    this.updateBodyScroll();
  }

  closeMobileSidebar(): void {
    this.isMobileSidebarOpen = false;
    this.updateBodyScroll();
  }

  // Update body scroll based on sidebar state
  private updateBodyScroll(): void {
    if (this.isMobileSidebarOpen) {
      this.renderer.addClass(document.body, 'mobile-sidebar-open');
    } else {
      this.renderer.removeClass(document.body, 'mobile-sidebar-open');
    }
  }

  // Navigation methods
  navigateToProfile(): void {
    this.router.navigate(['/profile']);
  }

  logout(): void {
    this.authService.logout();
  }
}
