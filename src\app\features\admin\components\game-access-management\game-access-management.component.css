/* Game Access Management Component Styles */

/* Custom scrollbar for table */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Form input focus states */
input:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

/* Table row hover effects */
tbody tr:hover {
  background-color: rgba(51, 65, 85, 0.2);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Loading spinner container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Status badge animations */
.status-badge {
  transition: all 0.2s ease-in-out;
}

.status-badge:hover {
  transform: scale(1.05);
}

/* Form validation styles */
.form-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive table adjustments */
@media (max-width: 768px) {
  .table-container {
    font-size: 0.875rem;
  }
  
  .table-container th,
  .table-container td {
    padding: 0.5rem 0.75rem;
  }
}

/* Action buttons container */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.action-buttons button {
  padding: 0.375rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
}

.action-buttons button:hover {
  background-color: rgba(51, 65, 85, 0.3);
}

/* Empty state styling */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
}

.empty-state svg {
  margin: 0 auto 1rem;
  opacity: 0.6;
}

/* Pagination styling */
.pagination-container {
  background-color: rgba(30, 41, 59, 0.4);
  border-top: 1px solid rgba(51, 65, 85, 0.5);
}

.pagination-button {
  transition: all 0.2s ease-in-out;
}

.pagination-button:hover:not(:disabled) {
  background-color: rgba(51, 65, 85, 0.7);
  transform: translateY(-1px);
}

.pagination-button.active {
  background-color: rgb(147, 51, 234);
  color: white;
}

/* Form section styling */
.form-section {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(51, 65, 85, 0.4);
}

/* Header gradient */
.header-icon {
  background: linear-gradient(135deg, rgb(147, 51, 234) 0%, rgb(51, 65, 85) 100%);
}

/* Filter section styling */
.filter-section {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(8px);
}

/* Table styling */
.table-section {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(51, 65, 85, 0.4);
}

.table-header {
  background: rgba(30, 41, 59, 0.6);
}

/* Success/Error message styling */
.success-message {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: rgb(74, 222, 128);
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: rgb(248, 113, 113);
}
